@{
    RootModule = 'BehavioralEvasion.psm1'
    ModuleVersion = '1.0.0'
    GUID = 'f8b2d5e1-4c7a-4f9e-8d6c-2a5b8f3e9c2d'
    Author = 'Anti-VM Detection System'
    Description = 'Behavioral evasion module for Performance, WMI, and User Simulation'
    
    FunctionsToExport = @(
        'Invoke-BehavioralEvasion',
        'Invoke-PerformanceSimulation',
        'Invoke-WMISpoofing',
        'Invoke-UserSimulation'
    )
    
    RequiredModules = @(
        @{ ModuleName = 'Microsoft.PowerShell.Management'; ModuleVersion = '*******' }
    )
    
    PowerShellVersion = '5.1'
    DotNetFrameworkVersion = '4.7.2'
}

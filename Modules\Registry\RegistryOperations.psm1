# Registry Operations Module
# Registry modification and cleanup for VM detection bypass

# Import required modules
Import-Module "$PSScriptRoot\..\Core\Logging\Logging.psm1" -Force
Import-Module "$PSScriptRoot\..\Core\Utilities\Utilities.psm1" -Force
Import-Module "$PSScriptRoot\RegistryPrivileges\RegistryPrivileges.psm1" -Force

# Registry Modification Functions
function Invoke-RegistryModification {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting registry modification..." "Info"
    
    try {
        # Ensure registry privileges
        Enable-RegistryPrivileges
        
        $vmDetectionKeys = @(
            @{ Path = 'HKLM:\SOFTWARE\VMware, Inc.'; Action = 'Delete' },
            @{ Path = 'HKLM:\SOFTWARE\Oracle\VirtualBox Guest Additions'; Action = 'Delete' },
            @{ Path = 'HKLM:\SYSTEM\ControlSet001\Services\VBoxGuest'; Action = 'Delete' },
            @{ Path = 'HKLM:\SYSTEM\ControlSet001\Services\VBoxMouse'; Action = 'Delete' },
            @{ Path = 'HKLM:\SYSTEM\ControlSet001\Services\VBoxService'; Action = 'Delete' },
            @{ Path = 'HKLM:\SYSTEM\ControlSet001\Services\VBoxSF'; Action = 'Delete' },
            @{ Path = 'HKLM:\SYSTEM\ControlSet001\Services\VBoxVideo'; Action = 'Delete' },
            @{ Path = 'HKLM:\SOFTWARE\Microsoft\Virtual Machine\Guest\Parameters'; Action = 'Delete' },
            @{ Path = 'HKLM:\SYSTEM\ControlSet001\Services\vmci'; Action = 'Delete' },
            @{ Path = 'HKLM:\SYSTEM\ControlSet001\Services\vmhgfs'; Action = 'Delete' },
            @{ Path = 'HKLM:\SYSTEM\ControlSet001\Services\vmmouse'; Action = 'Delete' },
            @{ Path = 'HKLM:\SYSTEM\ControlSet001\Services\vmrawdsk'; Action = 'Delete' },
            @{ Path = 'HKLM:\SYSTEM\ControlSet001\Services\vmusbmouse'; Action = 'Delete' },
            @{ Path = 'HKLM:\SYSTEM\ControlSet001\Services\vmvss'; Action = 'Delete' },
            @{ Path = 'HKLM:\SYSTEM\ControlSet001\Services\vmscsi'; Action = 'Delete' },
            @{ Path = 'HKLM:\SYSTEM\ControlSet001\Services\vmxnet'; Action = 'Delete' },
            @{ Path = 'HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Oracle VM VirtualBox Guest Additions'; Action = 'Delete' },
            @{ Path = 'HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\VMware Tools'; Action = 'Delete' }
        )
        
        $modifiedCount = 0
        foreach ($keyInfo in $vmDetectionKeys) {
            if (Test-Path $keyInfo.Path) {
                try {
                    Remove-Item -Path $keyInfo.Path -Recurse -Force -ErrorAction Stop
                    Write-ModuleLog "Removed VM detection key: $($keyInfo.Path)" "Debug"
                    $modifiedCount++
                }
                catch {
                    Write-ModuleLog "Failed to remove key $($keyInfo.Path): $($_.Exception.Message)" "Warning"
                }
            }
        }
        
        Write-ModuleLog "Registry modification completed: $modifiedCount keys processed" "Info"
        return @{ Success = $true; Message = "Removed $modifiedCount VM detection registry keys" }
    }
    catch {
        Write-ModuleLog "Registry modification failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

# Registry Cleanup Functions
function Invoke-RegistryCleanup {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting registry cleanup..." "Info"
    
    try {
        # Clean up VM-related registry artifacts
        $cleanupKeys = @(
            @{ Path = 'HKLM:\SYSTEM\CurrentControlSet\Enum\IDE'; Pattern = '*VBOX*|*VMWARE*|*QEMU*' },
            @{ Path = 'HKLM:\SYSTEM\CurrentControlSet\Enum\SCSI'; Pattern = '*VBOX*|*VMWARE*|*QEMU*' },
            @{ Path = 'HKLM:\SYSTEM\CurrentControlSet\Control\DeviceClasses'; Pattern = '*vbox*|*vmware*' },
            @{ Path = 'HKCU:\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\ComDlg32'; Pattern = '*virtualbox*|*vmware*' }
        )
        
        $cleanedCount = 0
        foreach ($cleanup in $cleanupKeys) {
            if (Test-Path $cleanup.Path) {
                $subKeys = Get-ChildItem -Path $cleanup.Path -Recurse -ErrorAction SilentlyContinue
                foreach ($subKey in $subKeys) {
                    if ($subKey.Name -like $cleanup.Pattern) {
                        try {
                            Remove-Item -Path $subKey.PSPath -Recurse -Force -ErrorAction Stop
                            Write-ModuleLog "Cleaned VM artifact: $($subKey.PSPath)" "Debug"
                            $cleanedCount++
                        }
                        catch {
                            Write-ModuleLog "Failed to clean $($subKey.PSPath): $($_.Exception.Message)" "Warning"
                        }
                    }
                }
            }
        }
        
        # Clean registry values containing VM signatures
        $valueCleanups = @(
            @{ Path = 'HKLM:\HARDWARE\DESCRIPTION\System'; Names = @('SystemBiosVersion', 'VideoBiosVersion') },
            @{ Path = 'HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion'; Names = @('RegisteredOwner', 'RegisteredOrganization') }
        )
        
        foreach ($cleanup in $valueCleanups) {
            if (Test-Path $cleanup.Path) {
                foreach ($valueName in $cleanup.Names) {
                    try {
                        $value = Get-ItemProperty -Path $cleanup.Path -Name $valueName -ErrorAction SilentlyContinue
                        if ($value -and ($value.$valueName -match 'vbox|vmware|qemu|virtual|hyperv')) {
                            Set-RegistryValue -Path $cleanup.Path -Name $valueName -Value "Default System" -Type "String"
                            Write-ModuleLog "Cleaned VM signature from $($cleanup.Path)\$valueName" "Debug"
                            $cleanedCount++
                        }
                    }
                    catch {
                        Write-ModuleLog "Failed to clean value $valueName from $($cleanup.Path): $($_.Exception.Message)" "Warning"
                    }
                }
            }
        }
        
        Write-ModuleLog "Registry cleanup completed: $cleanedCount items processed" "Info"
        return @{ Success = $true; Message = "Cleaned $cleanedCount VM registry artifacts" }
    }
    catch {
        Write-ModuleLog "Registry cleanup failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

# Main Registry Operations Function
function Invoke-RegistryOperations {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting registry operations..." "Info"
    $results = @()
    
    # Execute registry operations
    if ($Config.Modules.Registry.RegistryModification.Enabled) {
        $results += Invoke-RegistryModification -Config $Config
    }
    
    if ($Config.Modules.Registry.RegistryCleanup.Enabled) {
        $results += Invoke-RegistryCleanup -Config $Config
    }
    
    $successCount = ($results | Where-Object { $_.Success }).Count
    $totalCount = $results.Count
    
    Write-ModuleLog "Registry operations completed: $successCount/$totalCount successful" "Info"
    
    return @{
        Success = $successCount -eq $totalCount
        Results = $results
        Summary = "Registry operations: $successCount/$totalCount modules successful"
    }
}

# Main orchestration function (called by main script)
function Invoke-Registry {
    [CmdletBinding()]
    param(
        [hashtable]$Config
    )
    
    Write-ModuleLog "=== Registry Module Execution Started ===" "Info"
    
    try {
        $results = @()
        $modifiedComponents = @()
        
        # Execute registry operations based on configuration
        if ($Config.Modules.Registry.RegistryModification.Enabled) {
            $result = Invoke-RegistryModification -Config $Config
            $results += $result
            if ($result.Success) {
                $modifiedComponents += "Registry Modification"
            }
        }
        
        if ($Config.Modules.Registry.RegistryCleanup.Enabled) {
            $result = Invoke-RegistryCleanup -Config $Config
            $results += $result
            if ($result.Success) {
                $modifiedComponents += "Registry Cleanup"
            }
        }
        
        $successCount = ($results | Where-Object { $_.Success }).Count
        $totalCount = $results.Count
        
        Write-ModuleLog "Registry module completed: $successCount/$totalCount operations successful" "Info"
        
        return @{
            Success = $successCount -gt 0
            ModifiedComponents = $modifiedComponents
            Summary = "Registry operations: $successCount/$totalCount successful"
            Details = $results
        }
    }
    catch {
        Write-ModuleLog "Registry module failed: $($_.Exception.Message)" "Error"
        return @{
            Success = $false
            ModifiedComponents = @()
            Summary = "Registry module failed: $($_.Exception.Message)"
        }
    }
}

# Export functions
Export-ModuleMember -Function @(
    'Invoke-Registry',
    'Invoke-RegistryOperations',
    'Invoke-RegistryModification',
    'Invoke-RegistryCleanup'
)

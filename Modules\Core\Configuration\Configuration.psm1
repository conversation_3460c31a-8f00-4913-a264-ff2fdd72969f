# Configuration Module - Simplified Version
function Load-AntiVMConfiguration {
    param([string]$ConfigPath)
    
    if (-not (Test-Path $ConfigPath)) {
        throw "Configuration file not found: $ConfigPath"
    }
    
    try {
        $config = Import-PowerShellDataFile -Path $ConfigPath
        return $config
    }
    catch {
        throw "Failed to load configuration: $($_.Exception.Message)"
    }
}

function Get-ModuleConfiguration {
    param([string]$ModulePath)
    return @{ enabled = $true }
}

Export-ModuleMember -Function @('Load-AntiVMConfiguration', 'Get-ModuleConfiguration')

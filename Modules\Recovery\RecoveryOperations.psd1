@{
    RootModule = 'RecoveryOperations.psm1'
    ModuleVersion = '1.0.0'
    GUID = 'b5d9f3a2-8c7e-4f1a-9d6b-3c8f5a2e7b4d'
    Author = 'Anti-VM Detection System'
    Description = 'Recovery operations module for system rollback and recovery'
    
    FunctionsToExport = @(
        'Invoke-RecoveryOperations',
        'Invoke-SystemRollback',
        'Test-RecoveryIntegrity'
    )
    
    RequiredModules = @(
        @{ ModuleName = 'Microsoft.PowerShell.Management'; ModuleVersion = '*******' }
    )
    
    PowerShellVersion = '5.1'
    DotNetFrameworkVersion = '4.7.2'
}

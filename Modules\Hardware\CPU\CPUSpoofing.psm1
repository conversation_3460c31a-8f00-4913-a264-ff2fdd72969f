#Requires -Version 5.1

<#
.SYNOPSIS
    Hardware CPU Spoofing Module for Anti-VM Detection Toolkit

.DESCRIPTION
    Implements comprehensive CPU characteristics spoofing including processor information,
    cache specifications, feature sets, and microcode details to evade VM detection.

.NOTES
    Module: Hardware.CPU
    Author: Cybersecurity Research Team
    Version: 2.0-Modular
    Dependencies: Core.Logging, Core.Utilities, Registry.RegistryPrivileges
#>

# Module-level variables
$script:ModuleConfig = $null
$script:HardwareConfig = $null
$script:ModifiedComponents = @()

#region Public Functions

function Initialize-CPUSpoofing {
    <#
    .SYNOPSIS
        Initializes the CPU spoofing module with configuration
    
    .PARAMETER Config
        Configuration object
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [PSCustomObject]$Config
    )
    
    $script:ModuleConfig = $Config.modules.hardware.cpu
    $script:HardwareConfig = $Config.hardware.cpu
    Write-Log "CPU Spoofing module initialized" -Level Debug
}

function Invoke-CPUSpoofing {
    <#
    .SYNOPSIS
        Main CPU spoofing function that coordinates all CPU spoofing operations
    #>
    [CmdletBinding()]
    param()
    
    if (-not $script:ModuleConfig.enabled) {
        Write-Log "CPU spoofing module disabled in configuration" -Level Info
        return
    }
    
    Write-Log "Implementing comprehensive CPU characteristics spoofing..." -Level Info
    
    try {
        # Spoof processor information for all CPU cores
        if ($script:ModuleConfig.spoofProcessorInfo) {
            Invoke-ProcessorInfoSpoofing
        }
        
        # Modify CPU feature set to remove hypervisor indicators
        if ($script:ModuleConfig.modifyFeatureSet) {
            Invoke-CPUFeatureSetModification
        }
        
        # Update cache information
        if ($script:ModuleConfig.updateCacheInfo) {
            Invoke-CPUCacheInfoUpdate
        }
        
        # Generate realistic CPU serial
        if ($script:ModuleConfig.generateRealisticSerial) {
            Invoke-CPUSerialGeneration
        }
        
        # Update system-wide CPU information
        Invoke-SystemWideCPUUpdate
        
        Write-Log "Comprehensive CPU spoofing implemented successfully" -Level Info
        return $script:ModifiedComponents
    }
    catch {
        Write-Log "CPU spoofing failed: $($_.Exception.Message)" -Level Error
        throw
    }
}

function Get-CPUSpoofingResults {
    <#
    .SYNOPSIS
        Returns the results of CPU spoofing operations
    #>
    [CmdletBinding()]
    param()
    
    return @{
        ModifiedComponents = $script:ModifiedComponents
        HardwareConfig = $script:HardwareConfig
        Success = $script:ModifiedComponents.Count -gt 0
    }
}

#endregion

#region Private Functions

function Invoke-ProcessorInfoSpoofing {
    <#
    .SYNOPSIS
        Spoofs processor information for all CPU cores
    #>
    [CmdletBinding()]
    param()
    
    try {
        # Enhanced CPU spoofing with all processor cores
        $cpuCores = 0..($script:HardwareConfig.cores - 1)
        
        foreach ($coreIndex in $cpuCores) {
            $cpuPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\$coreIndex"
            if (Test-Path $cpuPath) {
                Set-RegistryKeyOwnership -RegistryPath $cpuPath | Out-Null
                
                # Comprehensive Intel CPU specifications
                Set-ItemProperty -Path $cpuPath -Name "ProcessorNameString" -Value $script:HardwareConfig.brand -Force -ErrorAction SilentlyContinue
                Set-ItemProperty -Path $cpuPath -Name "VendorIdentifier" -Value $script:HardwareConfig.vendor -Force -ErrorAction SilentlyContinue
                Set-ItemProperty -Path $cpuPath -Name "Identifier" -Value "Intel64 Family $($script:HardwareConfig.family) Model $($script:HardwareConfig.model) Stepping $($script:HardwareConfig.stepping)" -Force -ErrorAction SilentlyContinue
                
                # CPU frequency settings
                Set-ItemProperty -Path $cpuPath -Name "~MHz" -Value $script:HardwareConfig.baseClock -Force -ErrorAction SilentlyContinue
                Set-ItemProperty -Path $cpuPath -Name "Component Information" -Value "Processor" -Force -ErrorAction SilentlyContinue
                
                Write-Log "Enhanced CPU core $coreIndex spoofing: $($script:HardwareConfig.brand)" -Level Debug
                $script:ModifiedComponents += "CPU-Core-$coreIndex"
            }
        }
        
        Write-Log "Processor information spoofing completed for $($script:HardwareConfig.cores) cores" -Level Debug
    }
    catch {
        Write-Log "Processor info spoofing failed: $($_.Exception.Message)" -Level Error
        throw
    }
}

function Invoke-CPUFeatureSetModification {
    <#
    .SYNOPSIS
        Modifies CPU feature set to remove hypervisor indicators
    #>
    [CmdletBinding()]
    param()
    
    try {
        $cpuCores = 0..($script:HardwareConfig.cores - 1)
        
        foreach ($coreIndex in $cpuCores) {
            $cpuPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\$coreIndex"
            if (Test-Path $cpuPath) {
                Set-RegistryKeyOwnership -RegistryPath $cpuPath | Out-Null
                
                # Remove hypervisor bit from feature set (CPUID leaf 0x1, ECX bit 31)
                Set-ItemProperty -Path $cpuPath -Name "FeatureSet" -Value 0x178BFBFF -Force -ErrorAction SilentlyContinue
                
                # Intel microcode and stepping
                $intelMicrocode = Get-Random -Minimum 0x01000000 -Maximum 0x01FFFFFF
                Set-ItemProperty -Path $cpuPath -Name "Update Revision" -Value $intelMicrocode -Force -ErrorAction SilentlyContinue
                
                Write-Log "Modified CPU feature set for core $coreIndex (removed hypervisor bit)" -Level Debug
            }
        }
        
        $script:ModifiedComponents += "CPU-FeatureSet-All-Cores"
        Write-Log "CPU feature set modification completed" -Level Debug
    }
    catch {
        Write-Log "CPU feature set modification failed: $($_.Exception.Message)" -Level Error
        throw
    }
}

function Invoke-CPUCacheInfoUpdate {
    <#
    .SYNOPSIS
        Updates CPU cache information to match realistic values
    #>
    [CmdletBinding()]
    param()
    
    try {
        $cpuCores = 0..($script:HardwareConfig.cores - 1)
        
        foreach ($coreIndex in $cpuCores) {
            $cpuPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\$coreIndex"
            if (Test-Path $cpuPath) {
                Set-RegistryKeyOwnership -RegistryPath $cpuPath | Out-Null
                
                # CPU Cache information (L1, L2, L3)
                Set-ItemProperty -Path $cpuPath -Name "Level1InstructionCache" -Value $script:HardwareConfig.cacheL1 -Force -ErrorAction SilentlyContinue
                Set-ItemProperty -Path $cpuPath -Name "Level1DataCache" -Value $script:HardwareConfig.cacheL1 -Force -ErrorAction SilentlyContinue
                Set-ItemProperty -Path $cpuPath -Name "Level2Cache" -Value $script:HardwareConfig.cacheL2 -Force -ErrorAction SilentlyContinue
                Set-ItemProperty -Path $cpuPath -Name "Level3Cache" -Value $script:HardwareConfig.cacheL3 -Force -ErrorAction SilentlyContinue
                
                Write-Log "Updated CPU cache info for core $coreIndex (L1:$($script:HardwareConfig.cacheL1), L2:$($script:HardwareConfig.cacheL2), L3:$($script:HardwareConfig.cacheL3))" -Level Debug
            }
        }
        
        $script:ModifiedComponents += "CPU-Cache-Info"
        Write-Log "CPU cache information update completed" -Level Debug
    }
    catch {
        Write-Log "CPU cache info update failed: $($_.Exception.Message)" -Level Error
        throw
    }
}

function Invoke-CPUSerialGeneration {
    <#
    .SYNOPSIS
        Generates and applies realistic CPU serial numbers
    #>
    [CmdletBinding()]
    param()
    
    try {
        $cpuCores = 0..($script:HardwareConfig.cores - 1)
        
        foreach ($coreIndex in $cpuCores) {
            $cpuPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\$coreIndex"
            if (Test-Path $cpuPath) {
                Set-RegistryKeyOwnership -RegistryPath $cpuPath | Out-Null
                
                # Generate realistic CPU serial number
                $cpuSerial = New-RealisticCPUSerial
                Set-ItemProperty -Path $cpuPath -Name "ProcessorSerialNumber" -Value $cpuSerial -Force -ErrorAction SilentlyContinue
                
                Write-Log "Generated realistic CPU serial for core $coreIndex : $cpuSerial" -Level Debug
            }
        }
        
        $script:ModifiedComponents += "CPU-Serial-Numbers"
        Write-Log "CPU serial number generation completed" -Level Debug
    }
    catch {
        Write-Log "CPU serial generation failed: $($_.Exception.Message)" -Level Error
        throw
    }
}

function Invoke-SystemWideCPUUpdate {
    <#
    .SYNOPSIS
        Updates system-wide CPU information and environment variables
    #>
    [CmdletBinding()]
    param()
    
    try {
        # Modify CPU information in HARDWARE\DESCRIPTION
        $hardwareDescPath = "HKLM:\HARDWARE\DESCRIPTION\System"
        if (Test-Path $hardwareDescPath) {
            Set-RegistryKeyOwnership -RegistryPath $hardwareDescPath | Out-Null
            
            # System-wide CPU information
            Set-ItemProperty -Path $hardwareDescPath -Name "ProcessorNameString" -Value $script:HardwareConfig.brand -Force -ErrorAction SilentlyContinue
            Set-ItemProperty -Path $hardwareDescPath -Name "CentralProcessor" -Value "Intel" -Force -ErrorAction SilentlyContinue
            
            # Intel chipset information
            Set-ItemProperty -Path $hardwareDescPath -Name "SystemBiosVersion" -Value "Intel Corp. - 1072009" -Force -ErrorAction SilentlyContinue
            
            Write-Log "Modified system-wide CPU description" -Level Debug
        }
        
        # Update WMI CPU environment variables
        $envPaths = @(
            "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion",
            "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Environment"
        )
        
        foreach ($envPath in $envPaths) {
            if (Test-Path $envPath) {
                Set-ItemProperty -Path $envPath -Name "PROCESSOR_IDENTIFIER" -Value "Intel64 Family $($script:HardwareConfig.family) Model $($script:HardwareConfig.model) Stepping $($script:HardwareConfig.stepping), $($script:HardwareConfig.vendor)" -Force -ErrorAction SilentlyContinue
                Set-ItemProperty -Path $envPath -Name "PROCESSOR_LEVEL" -Value $script:HardwareConfig.family.ToString() -Force -ErrorAction SilentlyContinue
                Set-ItemProperty -Path $envPath -Name "PROCESSOR_REVISION" -Value ($script:HardwareConfig.model * 256 + $script:HardwareConfig.stepping).ToString("X4") -Force -ErrorAction SilentlyContinue
                Set-ItemProperty -Path $envPath -Name "NUMBER_OF_PROCESSORS" -Value $script:HardwareConfig.cores -Force -ErrorAction SilentlyContinue
                
                Write-Log "Enhanced WMI CPU environment variables in: $envPath" -Level Debug
            }
        }
        
        # Spoof CPU-related ACPI information
        $acpiCpuPath = "HKLM:\HARDWARE\ACPI\FADT"
        if (Test-Path $acpiCpuPath) {
            Set-RegistryKeyOwnership -RegistryPath $acpiCpuPath | Out-Null
            
            # Intel FADT (Fixed ACPI Description Table) signatures
            Set-ItemProperty -Path $acpiCpuPath -Name "OemId" -Value "INTEL " -Force -ErrorAction SilentlyContinue
            Set-ItemProperty -Path $acpiCpuPath -Name "OemTableId" -Value "DESKTOP " -Force -ErrorAction SilentlyContinue
            
            Write-Log "Spoofed ACPI CPU information" -Level Debug
        }
        
        $script:ModifiedComponents += "CPU-System-Wide-Update"
        Write-Log "System-wide CPU update completed" -Level Debug
    }
    catch {
        Write-Log "System-wide CPU update failed: $($_.Exception.Message)" -Level Error
        throw
    }
}

#endregion

# Export public functions
Export-ModuleMember -Function @(
    'Initialize-CPUSpoofing',
    'Invoke-CPUSpoofing',
    'Get-CPUSpoofingResults'
)

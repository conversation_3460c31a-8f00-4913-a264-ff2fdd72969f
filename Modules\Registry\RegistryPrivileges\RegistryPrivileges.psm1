#Requires -Version 5.1

<#
.SYNOPSIS
    Registry Privileges Module for Anti-VM Detection Toolkit

.DESCRIPTION
    Provides advanced registry access capabilities through C# interop including
    privilege elevation, ownership management, and secure registry manipulation.

.NOTES
    Module: Registry.RegistryPrivileges
    Author: Cybersecurity Research Team
    Version: 2.0-Modular
    Dependencies: Core.Logging
#>

# Module-level variables
$script:ModuleConfig = $null
$script:PrivilegesInitialized = $false

# C# Interop for Registry Privileges
Add-Type @"
using System;
using System.Runtime.InteropServices;
using System.Security.Principal;
using Microsoft.Win32;

public class RegistryPrivileges
{
    [DllImport("advapi32.dll", SetLastError = true)]
    public static extern bool OpenProcessToken(IntPtr ProcessHandle, uint DesiredAccess, out IntPtr TokenHandle);
    
    [DllImport("advapi32.dll", SetLastError = true, CharSet = CharSet.Unicode)]
    public static extern bool LookupPrivilegeValue(string lpSystemName, string lpName, out long lpLuid);
    
    [DllImport("advapi32.dll", SetLastError = true)]
    public static extern bool AdjustTokenPrivileges(IntPtr TokenHandle, bool DisableAllPrivileges, ref TOKEN_PRIVILEGES NewState, uint BufferLength, IntPtr PreviousState, IntPtr ReturnLength);
    
    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern IntPtr GetCurrentProcess();
    
    [DllImport("kernel32.dll", SetLastError = true)]
    public static extern bool CloseHandle(IntPtr hObject);
    
    [StructLayout(LayoutKind.Sequential)]
    public struct LUID
    {
        public uint LowPart;
        public int HighPart;
    }
    
    [StructLayout(LayoutKind.Sequential)]
    public struct TOKEN_PRIVILEGES
    {
        public uint PrivilegeCount;
        public LUID Luid;
        public uint Attributes;
    }
    
    public const uint TOKEN_ADJUST_PRIVILEGES = 0x0020;
    public const uint TOKEN_QUERY = 0x0008;
    public const uint SE_PRIVILEGE_ENABLED = 0x0002;
    public const string SE_TAKE_OWNERSHIP_NAME = "SeTakeOwnershipPrivilege";
    public const string SE_RESTORE_NAME = "SeRestorePrivilege";
    public const string SE_BACKUP_NAME = "SeBackupPrivilege";
    
    public static bool EnablePrivilege(string privilegeName)
    {
        IntPtr tokenHandle = IntPtr.Zero;
        try
        {
            if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, out tokenHandle))
                return false;
                
            long luid;
            if (!LookupPrivilegeValue(null, privilegeName, out luid))
                return false;
                
            TOKEN_PRIVILEGES tokenPrivileges = new TOKEN_PRIVILEGES();
            tokenPrivileges.PrivilegeCount = 1;
            tokenPrivileges.Luid.LowPart = (uint)luid;
            tokenPrivileges.Luid.HighPart = (int)(luid >> 32);
            tokenPrivileges.Attributes = SE_PRIVILEGE_ENABLED;
            
            return AdjustTokenPrivileges(tokenHandle, false, ref tokenPrivileges, 0, IntPtr.Zero, IntPtr.Zero);
        }
        finally
        {
            if (tokenHandle != IntPtr.Zero)
                CloseHandle(tokenHandle);
        }
    }
}
"@

#region Public Functions

function Initialize-RegistryPrivileges {
    <#
    .SYNOPSIS
        Initializes registry privileges module with configuration
    
    .PARAMETER Config
        Configuration object
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [PSCustomObject]$Config
    )
    
    $script:ModuleConfig = $Config.modules.registry.registryPrivileges
    Write-Log "Registry Privileges module initialized" -Level Debug
}

function Enable-RegistryPrivileges {
    <#
    .SYNOPSIS
        Enables required registry privileges for advanced operations
    #>
    [CmdletBinding()]
    param()
    
    if (-not $script:ModuleConfig.enabled) {
        Write-Log "Registry privileges module disabled in configuration" -Level Info
        return $false
    }
    
    Write-Log "Enabling required registry privileges..." -Level Info
    
    try {
        $privilegesToEnable = @()
        
        if ($script:ModuleConfig.enableTakeOwnership) {
            $privilegesToEnable += "SeTakeOwnershipPrivilege"
        }
        if ($script:ModuleConfig.enableRestorePrivilege) {
            $privilegesToEnable += "SeRestorePrivilege"
        }
        if ($script:ModuleConfig.enableBackupPrivilege) {
            $privilegesToEnable += "SeBackupPrivilege"
        }
        
        $successCount = 0
        foreach ($privilege in $privilegesToEnable) {
            $result = [RegistryPrivileges]::EnablePrivilege($privilege)
            if ($result) {
                Write-Log "Enabled privilege: $privilege" -Level Debug
                $successCount++
            } else {
                Write-Log "Failed to enable privilege: $privilege" -Level Warning
            }
        }
        
        $script:PrivilegesInitialized = ($successCount -gt 0)
        Write-Log "Registry privileges configuration completed ($successCount/$($privilegesToEnable.Count) successful)" -Level Info
        
        return $script:PrivilegesInitialized
    }
    catch {
        Write-Log "Failed to configure registry privileges: $($_.Exception.Message)" -Level Error
        return $false
    }
}

function Set-RegistryKeyOwnership {
    <#
    .SYNOPSIS
        Takes ownership of a registry key for modification
    
    .PARAMETER RegistryPath
        PowerShell registry path (e.g., HKLM:\SOFTWARE\...)
        
    .PARAMETER Owner
        Target owner (defaults to current user)
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$RegistryPath,
        
        [Parameter(Mandatory = $false)]
        [string]$Owner = "Administrators"
    )
    
    try {
        # Convert PowerShell path to .NET registry path
        $regPath = $RegistryPath -replace '^HKLM:\\', 'HKEY_LOCAL_MACHINE\'
        $regPath = $regPath -replace '^HKCU:\\', 'HKEY_CURRENT_USER\'
        
        # Try icacls first for registry keys
        $result = & icacls $regPath /setowner "$env:USERNAME" /t /c 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Took ownership of registry key: $RegistryPath" -Level Debug
            return $true
        } else {
            # Try alternative method using PowerShell registry provider
            $acl = Get-Acl $RegistryPath -ErrorAction SilentlyContinue
            if ($acl) {
                $accessRule = New-Object System.Security.AccessControl.RegistryAccessRule(
                    [System.Security.Principal.WindowsIdentity]::GetCurrent().User,
                    "FullControl",
                    "ContainerInherit,ObjectInherit",
                    "None",
                    "Allow"
                )
                $acl.SetAccessRule($accessRule)
                Set-Acl $RegistryPath $acl -ErrorAction SilentlyContinue
                Write-Log "Set permissions for registry key: $RegistryPath" -Level Debug
                return $true
            }
        }
        
        return $false
    }
    catch {
        Write-Log "Failed to set ownership for $RegistryPath : $($_.Exception.Message)" -Level Debug
        return $false
    }
}

function Test-RegistryPrivilegesEnabled {
    <#
    .SYNOPSIS
        Tests if registry privileges have been successfully initialized
    #>
    [CmdletBinding()]
    param()
    
    return $script:PrivilegesInitialized
}

function Get-RegistryPrivilegesStatus {
    <#
    .SYNOPSIS
        Returns the status of registry privileges configuration
    #>
    [CmdletBinding()]
    param()
    
    return @{
        Initialized = $script:PrivilegesInitialized
        Configuration = $script:ModuleConfig
        SupportedPrivileges = @(
            "SeTakeOwnershipPrivilege",
            "SeRestorePrivilege", 
            "SeBackupPrivilege"
        )
    }
}

#endregion

# Export public functions
Export-ModuleMember -Function @(
    'Initialize-RegistryPrivileges',
    'Enable-RegistryPrivileges',
    'Set-RegistryKeyOwnership',
    'Test-RegistryPrivilegesEnabled',
    'Get-RegistryPrivilegesStatus'
)

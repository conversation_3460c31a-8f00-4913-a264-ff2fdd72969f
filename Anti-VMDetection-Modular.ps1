#Requires -RunAsAdministrator
#Requires -Version 5.1

<#
.SYNOPSIS
    Anti-VM Detection Toolkit - Modular Architecture Version

.DESCRIPTION
    Advanced modular anti-VM detection bypass system for cybersecurity research.
    This is the main orchestration script that dynamically loads and executes
    individual modules based on configuration settings.

    Target Malware Families (2024-2025):
    - LockBit, BlackCat/ALPHV, Royal Ransomware (VM-aware variants)
    - Emote<PERSON>, Qakbot, IcedID (banking trojans with VM detection)
    - Cobalt Strike beacons, Metasploit payloads
    - APT groups using VM-aware implants (APT29, APT40, Lazarus)

.PARAMETER ConfigFile
    Path to configuration file (default: config.psd1)

.PARAMETER LogLevel
    Logging verbosity: Debug, Info, Warning, Error (default: from config)

.PARAMETER BackupPath
    Directory for storing system backups (default: .\Backups)

.PARAMETER RollbackMode
    Restore system from previous backup

.PARAMETER ModulesPath
    Path to modules directory (default: .\Modules)

.PARAMETER DryRun
    Simulate execution without making actual changes

.EXAMPLE
    .\Anti-VMDetection-Modular.ps1 -LogLevel Debug
    .\Anti-VMDetection-Modular.ps1 -RollbackMode -BackupPath "D:\Backups\********"
    .\Anti-VMDetection-Modular.ps1 -DryRun -ConfigFile "custom-config.psd1"

.NOTES
    Author: Cybersecurity Research Team
    Version: 2.0-Modular
    Requires: Windows 10/11, PowerShell 5.1+, Administrative privileges
    Tested: VMware Workstation 16+/17+

    WARNING: This tool modifies critical system components. Use only in isolated
    research environments. Create full system backups before deployment.
#>

param(
    [Parameter(Mandatory = $false)]
    [string]$ConfigFile = "config.psd1",
    
    [Parameter(Mandatory = $false)]
    [ValidateSet("Debug", "Info", "Warning", "Error")]
    [string]$LogLevel,
    
    [Parameter(Mandatory = $false)]
    [string]$BackupPath = ".\Backups",
    
    [Parameter(Mandatory = $false)]
    [switch]$RollbackMode,
    
    [Parameter(Mandatory = $false)]
    [string]$ModulesPath = ".\Modules",
    
    [Parameter(Mandatory = $false)]
    [switch]$DryRun
)

# Global script variables
$script:LoadedModules = @{}
$script:ModuleExecutionResults = @{}
$script:GlobalConfig = $null
$script:BackupLocation = $null
$script:StartTime = Get-Date
$script:ModifiedComponents = @{}

# Fallback logging function in case module loading fails
function Write-ModuleLog {
    param([string]$Message, [string]$Level = "Info")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    $color = switch($Level) {
        "Error" { "Red" }
        "Warning" { "Yellow" }
        "Debug" { "Gray" }
        default { "White" }
    }
    Write-Host $logMessage -ForegroundColor $color
}

function Write-Progress-Log {
    param([string]$Activity, [string]$Status, [int]$PercentComplete)
    Write-Progress -Activity $Activity -Status $Status -PercentComplete $PercentComplete
}

function Get-LogFile {
    return "$env:TEMP\AntiVM-$(Get-Date -Format 'yyyyMMdd').log"
}

#region Module Loading and Management

function Import-AntiVMModule {
    <#
    .SYNOPSIS
        Dynamically imports a module from the modules directory
    
    .PARAMETER ModuleName
        Module category name (e.g., "Hardware", "Registry")
        
    .PARAMETER ModulesBasePath
        Base path for modules directory
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$ModuleName,
        
        [Parameter(Mandatory = $true)]
        [string]$ModulesBasePath
    )
    
    try {
        # Map module names to actual module files
        $moduleMap = @{
            'Core' = @{
                'Logging' = 'Core\Logging\Logging.psm1'
                'Configuration' = 'Core\Configuration\Configuration.psm1'
                'Validation' = 'Core\Validation\Validation.psm1'
                'Utilities' = 'Core\Utilities\Utilities.psm1'
            }
            'Registry' = 'Registry\RegistryOperations.psm1'
            'Hardware' = 'Hardware\HardwareSpoofing.psm1'
            'System' = 'System\SystemSpoofing.psm1'
            'FileSystem' = 'FileSystem\FileSystemOperations.psm1'
            'Behavioral' = 'Behavioral\BehavioralEvasion.psm1'
            'Advanced' = 'Advanced\AdvancedBypass.psm1'
        }
        
        if ($ModuleName -eq 'Core') {
            # Load all core modules
            foreach ($coreModule in $moduleMap['Core'].GetEnumerator()) {
                $moduleFile = Join-Path $ModulesBasePath $coreModule.Value
                if (Test-Path $moduleFile) {
                    $importedModule = Import-Module $moduleFile -Force -PassThru -ErrorAction Stop
                    $script:LoadedModules["Core.$($coreModule.Key)"] = $importedModule
                    Write-Host "[INFO] Loaded core module: $($coreModule.Key)" -ForegroundColor Green
                }
            }
        }
        else {
            # Load specific module
            $moduleFile = Join-Path $ModulesBasePath $moduleMap[$ModuleName]
            
            if (-not (Test-Path $moduleFile)) {
                throw "Module file not found: $moduleFile"
            }
            
            # Import the module
            $importedModule = Import-Module $moduleFile -Force -PassThru -ErrorAction Stop
            $script:LoadedModules[$ModuleName] = $importedModule
            
            Write-Host "[INFO] Loaded module: $ModuleName" -ForegroundColor Green
            return $importedModule
        }
    }
    catch {
        Write-Host "[ERROR] Failed to load module $ModuleName : $($_.Exception.Message)" -ForegroundColor Red
        throw
    }
}

function Initialize-CoreModules {
    <#
    .SYNOPSIS
        Initializes all core infrastructure modules
    #>
    [CmdletBinding()]
    param()
    
    try {
        Write-Host "[INFO] Initializing core infrastructure modules..." -ForegroundColor Cyan
        
        # Load core modules
        Import-AntiVMModule -ModuleName "Core" -ModulesBasePath $ModulesPath
        
        # Initialize logging first and import functions globally
        if ($script:LoadedModules.ContainsKey("Core.Logging")) {
            $tempConfig = @{ Modules = @{ Core = @{ Logging = @{ 
                Enabled = $true
                LogLevel = if ($LogLevel) { $LogLevel } else { "Info" }
                LogToFile = $true
                LogToConsole = $true
                ShowProgress = $true
            }}}}
            Initialize-Logging -Config $tempConfig
            
            # Import logging functions to global scope
            Import-Module ($script:LoadedModules["Core.Logging"].Path) -Global -Force
        }
        
        Write-ModuleLog "Core modules initialization completed" "Info"
        return $true
    }
    catch {
        Write-Host "[ERROR] Core modules initialization failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Import-ConfiguredModules {
    <#
    .SYNOPSIS
        Imports modules based on configuration settings
    #>
    [CmdletBinding()]
    param()
    
    try {
        Write-Host "[INFO] Loading modules based on configuration..." -ForegroundColor Cyan
        
        # Get load order from configuration
        $loadOrder = $script:GlobalConfig.LoadOrder
        $loadedCount = 0
        
        foreach ($moduleName in $loadOrder) {
            try {
                # Skip core modules (already loaded)
                if ($moduleName -eq "Core") {
                    continue
                }
                
                # Check if module category is enabled
                $moduleConfig = $script:GlobalConfig.Modules[$moduleName]
                if (-not $moduleConfig) {
                    Write-Host "[WARNING] Module configuration not found: $moduleName" -ForegroundColor Yellow
                    continue
                }
                
                # Check if any sub-modules in category are enabled
                $hasEnabledSubmodules = $false
                foreach ($subModule in $moduleConfig.GetEnumerator()) {
                    if ($subModule.Value.Enabled) {
                        $hasEnabledSubmodules = $true
                        break
                    }
                }
                
                if ($hasEnabledSubmodules) {
                    # Load the module if not already loaded
                    if (-not $script:LoadedModules.ContainsKey($moduleName)) {
                        Import-AntiVMModule -ModuleName $moduleName -ModulesBasePath $ModulesPath
                        $loadedCount++
                    }
                } else {
                    Write-Host "[DEBUG] Module category disabled: $moduleName" -ForegroundColor Gray
                }
            }
            catch {
                Write-Host "[WARNING] Failed to load/initialize module $moduleName : $($_.Exception.Message)" -ForegroundColor Yellow
                # Continue with other modules
            }
        }
        
        Write-Host "[INFO] Module loading completed. Loaded $loadedCount modules." -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "[ERROR] Module loading failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

#endregion

#region Execution Engine

function Start-ModularAntiVMDetection {
    <#
    .SYNOPSIS
        Main execution function for the modular anti-VM detection system
    #>
    [CmdletBinding()]
    param()
    
    Write-ModuleLog "=== Anti-VM Detection Toolkit (Modular) Started ===" "Info"
    Write-ModuleLog "Target: Modern malware VM detection bypass for cybersecurity research" "Info"
    Write-ModuleLog "Environment: VMware Workstation 16+/17+ on Windows 10/11" "Info"
    
    if ($DryRun) {
        Write-ModuleLog "DRY RUN MODE: No actual changes will be made" "Warning"
    }
    
    try {
        # Validate prerequisites using core validation module
        if ($script:LoadedModules.ContainsKey("Core.Validation")) {
            Write-Progress-Log -Activity "Anti-VM Detection" -Status "Validating Prerequisites" -PercentComplete 5
            Invoke-PrerequisiteValidation
        }
        
        # Enable registry privileges
        if ($script:LoadedModules.ContainsKey("Registry.RegistryPrivileges")) {
            Write-Progress-Log -Activity "Anti-VM Detection" -Status "Enabling Registry Privileges" -PercentComplete 10
            Enable-RegistryPrivileges
        }
        
        # Create system backup
        if ($script:GlobalConfig.safety.createBackups -and $script:LoadedModules.ContainsKey("Recovery.Backup")) {
            Write-Progress-Log -Activity "Anti-VM Detection" -Status "Creating System Backup" -PercentComplete 15
            $script:BackupLocation = New-SystemBackup -BackupDir $BackupPath
            Write-ModuleLog "System backup created at: $script:BackupLocation" "Info"
        }
        
        # Confirm execution if required
        if ($script:GlobalConfig.safety.requireConfirmation -and -not $DryRun) {
            $confirmation = Read-Host "This will modify critical system components. Continue? (y/N)"
            if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
                Write-ModuleLog "Operation cancelled by user" "Info"
                return
            }
        }
        
        # Execute modules in configured order
        $progressStep = 70 / $script:GlobalConfig.LoadOrder.Count
        $currentProgress = 20
        
        foreach ($moduleName in $script:GlobalConfig.LoadOrder) {
            try {
                # Skip core modules (already executed)
                if ($moduleName -eq "Core") {
                    continue
                }
                
                # Check if module category is enabled
                $moduleConfig = $script:GlobalConfig.Modules[$moduleName]
                if (-not $moduleConfig) {
                    Write-ModuleLog "Module configuration not found: $moduleName" "Warning"
                    continue
                }
                
                # Check if any sub-modules in category are enabled
                $hasEnabledSubmodules = $false
                foreach ($subModule in $moduleConfig.GetEnumerator()) {
                    if ($subModule.Value.Enabled) {
                        $hasEnabledSubmodules = $true
                        break
                    }
                }
                
                if (-not $hasEnabledSubmodules) {
                    Write-ModuleLog "Skipping disabled module category: $moduleName" "Debug"
                    continue
                }
                
                Write-Progress-Log -Activity "Anti-VM Detection" -Status "Executing $moduleName" -PercentComplete $currentProgress
                
                # Execute the main function for this module
                $mainFunctionName = "Invoke-$moduleName"
                $functionExists = Get-Command $mainFunctionName -ErrorAction SilentlyContinue
                
                if ($functionExists) {
                    if ($DryRun) {
                        Write-ModuleLog "DRY RUN: Would execute $mainFunctionName" "Info"
                        $result = @{ Success = $true; DryRun = $true }
                    } else {
                        Write-ModuleLog "Executing module: $moduleName" "Info"
                        $result = & $mainFunctionName -Config $script:GlobalConfig
                        Write-ModuleLog "Module $moduleName completed successfully" "Info"
                    }
                    
                    $script:ModuleExecutionResults[$moduleName] = $result
                } else {
                    Write-ModuleLog "Main function not found for module: $moduleName ($mainFunctionName)" "Warning"
                }
                
                $currentProgress += $progressStep
            }
            catch {
                Write-ModuleLog "Module execution failed: $moduleName - $($_.Exception.Message)" "Error"
                $script:ModuleExecutionResults[$moduleName] = @{ Success = $false; Error = $_.Exception.Message }
                
                if ($script:GlobalConfig.execution.stopOnCriticalError) {
                    throw "Critical module failure: $moduleName"
                }
            }
        }
        
        Write-Progress-Log -Activity "Anti-VM Detection" -Status "Finalizing" -PercentComplete 95
        
        # Generate execution summary
        Write-ExecutionSummary
        
        Write-Progress-Log -Activity "Anti-VM Detection" -Status "Complete" -PercentComplete 100
        Write-ModuleLog "=== Anti-VM Detection Toolkit (Modular) Completed Successfully ===" "Info"
        
        if (-not $DryRun) {
            Write-ModuleLog "IMPORTANT: System restart recommended for full effect" "Warning"
            Write-ModuleLog "REMINDER: Use rollback function to restore original state when analysis is complete" "Warning"
        }
    }
    catch {
        Write-ModuleLog "Critical error during execution: $($_.Exception.Message)" "Error"
        if ($script:BackupLocation) {
            Write-ModuleLog "Check backup at $script:BackupLocation for recovery options" "Error"
        }
        throw
    }
}

function Write-ExecutionSummary {
    <#
    .SYNOPSIS
        Generates and displays execution summary
    #>
    [CmdletBinding()]
    param()
    
    Write-ModuleLog "=== EXECUTION SUMMARY ===" "Info"
    
    # Module execution statistics
    $totalModules = $script:ModuleExecutionResults.Count
    $successfulModules = ($script:ModuleExecutionResults.Values | Where-Object { $_.Success -eq $true }).Count
    $failedModules = $totalModules - $successfulModules
    
    Write-ModuleLog "Modules executed: $totalModules" "Info"
    Write-ModuleLog "Successful: $successfulModules" "Info"
    Write-ModuleLog "Failed: $failedModules" "Info"
    
    if ($script:BackupLocation) {
        Write-ModuleLog "Backup location: $script:BackupLocation" "Info"
    }
    Write-ModuleLog "Log file: $(Get-LogFile)" "Info"
    Write-ModuleLog "Execution time: $((Get-Date) - $script:StartTime)" "Info"
    
    # Show failed modules
    if ($failedModules -gt 0) {
        Write-ModuleLog "Failed modules:" "Warning"
        $script:ModuleExecutionResults.GetEnumerator() | Where-Object { $_.Value.Success -eq $false } | ForEach-Object {
            Write-ModuleLog "  - $($_.Key): $($_.Value.Error)" "Warning"
        }
    }
    
    # Aggregate modified components
    $allComponents = @()
    $script:ModuleExecutionResults.Values | ForEach-Object {
        if ($_.ModifiedComponents) {
            $allComponents += $_.ModifiedComponents
        }
    }
    
    Write-ModuleLog "Total components modified: $($allComponents.Count)" "Info"
    if ($script:GlobalConfig.output.showModifiedComponents -and $allComponents.Count -gt 0) {
        Write-ModuleLog "Modified components:" "Info"
        $allComponents | ForEach-Object { Write-ModuleLog "  - $_" "Info" }
    }
}

#endregion

#region Rollback Operations

function Start-SystemRollback {
    <#
    .SYNOPSIS
        Performs system rollback using the Recovery modules
    
    .PARAMETER BackupDirectory
        Directory containing backup files
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$BackupDirectory
    )
    
    try {
        Write-ModuleLog "Starting system rollback from backup: $BackupDirectory" "Info"
        
        # Load core modules for rollback
        if (-not (Initialize-CoreModules)) {
            throw "Failed to initialize core modules for rollback"
        }
        
        # Load configuration for rollback settings
        $script:GlobalConfig = Load-AntiVMConfiguration -ConfigPath $ConfigFile
        
        # Load rollback module
        Import-AntiVMModule -ModuleName "Recovery.Rollback" -ModulesBasePath $ModulesPath
        Initialize-RollbackOperations -Config $script:GlobalConfig
        
        # Perform rollback
        Invoke-SystemRollback -BackupDir $BackupDirectory
        
        Write-ModuleLog "System rollback completed successfully" "Info"
        Write-ModuleLog "IMPORTANT: System restart recommended to complete restoration" "Warning"
    }
    catch {
        Write-ModuleLog "System rollback failed: $($_.Exception.Message)" "Error"
        throw
    }
}

#endregion

#region Main Execution

try {
    # Initialize script
    Write-Host "Anti-VM Detection Toolkit - Modular Architecture v2.0" -ForegroundColor Cyan
    Write-Host "Cybersecurity Research Team" -ForegroundColor Gray
    Write-Host ""
    
    # Handle rollback mode
    if ($RollbackMode) {
        if (-not $BackupPath -or -not (Test-Path $BackupPath)) {
            throw "Valid backup path required for rollback mode"
        }
        Start-SystemRollback -BackupDirectory $BackupPath
        exit 0
    }
    
    # Validate modules directory
    if (-not (Test-Path $ModulesPath)) {
        throw "Modules directory not found: $ModulesPath"
    }
    
    # Initialize core infrastructure
    Write-Host "[INFO] Initializing core infrastructure..." -ForegroundColor Cyan
    if (-not (Initialize-CoreModules)) {
        throw "Failed to initialize core modules"
    }
    
    # Load configuration
    Write-ModuleLog "Loading configuration from: $ConfigFile" "Info"
    $script:GlobalConfig = Load-AntiVMConfiguration -ConfigPath $ConfigFile
    
    # Override log level if specified via parameter
    if ($LogLevel) {
        $script:GlobalConfig.Modules.Core.Logging.LogLevel = $LogLevel
        Write-ModuleLog "Log level overridden to: $LogLevel" "Info"
    }
    
    # Re-initialize logging with full configuration
    Initialize-Logging -Config $script:GlobalConfig
    
    # Load and initialize all configured modules
    if (Get-Command Write-ModuleLog -ErrorAction SilentlyContinue) {
        Write-ModuleLog "Loading configured modules..." "Info"
    } else {
        Write-Host "[INFO] Loading configured modules..." -ForegroundColor Cyan
    }
    if (-not (Import-ConfiguredModules)) {
        throw "Failed to load configured modules"
    }
    
    # Start main execution
    Start-ModularAntiVMDetection
    
    # Performance and stability monitoring
    if ($script:GlobalConfig.safety.performStabilityChecks -and -not $DryRun) {
        Write-ModuleLog "Performing post-execution stability check..." "Info"
        
        # Check critical services
        $criticalServices = @("Winmgmt", "EventLog", "RpcSs", "Dhcp")
        foreach ($service in $criticalServices) {
            $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
            if ($svc -and $svc.Status -ne "Running") {
                Write-ModuleLog "WARNING: Critical service not running: $service" "Warning"
            }
        }
        
        # Check system responsiveness
        $responseTest = Measure-Command { Get-Process | Out-Null }
        if ($responseTest.TotalSeconds -gt 5) {
            Write-ModuleLog "WARNING: System response time degraded ($($responseTest.TotalSeconds)s)" "Warning"
        }
        
        Write-ModuleLog "Stability check completed" "Info"
    }
    
    Write-ModuleLog "Script execution completed successfully. Log saved to: $(Get-LogFile)" "Info"
}
catch {
    $errorMessage = "Script execution failed: $($_.Exception.Message)"
    
    if (Get-Command Write-ModuleLog -ErrorAction SilentlyContinue) {
        Write-ModuleLog $errorMessage "Error"
    } else {
        Write-Host "[ERROR] $errorMessage" -ForegroundColor Red
    }
    
    exit 1
}
finally {
    # Cleanup: Remove loaded modules if needed
    if ($script:LoadedModules.Count -gt 0) {
        # Use Write-Host for cleanup since Write-ModuleLog may not be available
        Write-Host "[DEBUG] Cleaning up loaded modules..." -ForegroundColor Yellow
        $script:LoadedModules.Values | ForEach-Object {
            try {
                Remove-Module $_.Name -Force -ErrorAction SilentlyContinue
            }
            catch {
                # Ignore cleanup errors
            }
        }
    }
}

#endregion

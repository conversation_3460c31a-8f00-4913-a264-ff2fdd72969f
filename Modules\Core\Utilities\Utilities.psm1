# Utilities Module - Simplified Version
function Get-RandomGUID {
    return [System.Guid]::NewGuid().ToString()
}

function Get-RandomSerial {
    param([string]$Manufacturer = "Default")
    $random = Get-Random -Minimum 100000000 -Maximum 999999999
    return "$($Manufacturer[0..2] -join '')$random"
}

function Get-RandomMACAddress {
    $bytes = 1..6 | ForEach-Object { Get-Random -Minimum 0 -Maximum 255 }
    return ($bytes | ForEach-Object { $_.ToString("X2") }) -join "-"
}

function Set-RegistryValue {
    param(
        [string]$Path,
        [string]$Name,
        [object]$Value,
        [string]$Type
    )
    
    try {
        # Validate registry path format
        if (-not $Path.StartsWith("HKLM:") -and -not $Path.StartsWith("HKCU:") -and -not $Path.StartsWith("HKCR:")) {
            if (Get-Command "Write-ModuleLog" -ErrorAction SilentlyContinue) {
                Write-ModuleLog "Invalid registry path format: $Path" "Warning"
            }
            return $false
        }
        
        # Check if parent path exists and is accessible
        $parentPath = Split-Path $Path -Parent
        if ($parentPath -and -not (Test-Path $parentPath)) {
            try {
                New-Item -Path $parentPath -Force -ErrorAction Stop | Out-Null
            }
            catch {
                if (Get-Command "Write-ModuleLog" -ErrorAction SilentlyContinue) {
                    Write-ModuleLog "Cannot create parent registry path $parentPath : $($_.Exception.Message)" "Warning"
                }
                return $false
            }
        }
        
        # Create the target path if it doesn't exist
        if (-not (Test-Path $Path)) {
            try {
                New-Item -Path $Path -Force -ErrorAction Stop | Out-Null
            }
            catch {
                if (Get-Command "Write-ModuleLog" -ErrorAction SilentlyContinue) {
                    Write-ModuleLog "Cannot create registry path $Path : $($_.Exception.Message)" "Warning"
                }
                return $false
            }
        }
        
        # Handle large values that exceed UInt32 limits
        if ($Type -eq "DWord" -and $Value -is [string] -and [int64]$Value -gt [uint32]::MaxValue) {
            # Use QWord for large values
            Set-ItemProperty -Path $Path -Name $Name -Value ([int64]$Value) -Type "QWord" -Force -ErrorAction Stop
        }
        elseif ($Type -eq "DWord" -and $Value -is [int64] -and $Value -gt [uint32]::MaxValue) {
            # Use QWord for large values
            Set-ItemProperty -Path $Path -Name $Name -Value $Value -Type "QWord" -Force -ErrorAction Stop
        }
        else {
            Set-ItemProperty -Path $Path -Name $Name -Value $Value -Type $Type -Force -ErrorAction Stop
        }
        
        return $true
    }
    catch {
        if (Get-Command "Write-ModuleLog" -ErrorAction SilentlyContinue) {
            Write-ModuleLog "Failed to set registry value $Path\$Name : $($_.Exception.Message)" "Warning"
        } else {
            Write-Host "[WARNING] Failed to set registry value $Path\$Name : $($_.Exception.Message)" -ForegroundColor Yellow
        }
        return $false
    }
}

function Get-RandomDriverContent {
    param([string]$DriverName)
    # Return dummy driver content (simplified)
    $dummyBytes = 1..1024 | ForEach-Object { Get-Random -Minimum 0 -Maximum 255 }
    return [byte[]]$dummyBytes
}

Export-ModuleMember -Function @('Get-RandomGUID', 'Get-RandomSerial', 'Get-RandomMACAddress', 'Set-RegistryValue', 'Get-RandomDriverContent')

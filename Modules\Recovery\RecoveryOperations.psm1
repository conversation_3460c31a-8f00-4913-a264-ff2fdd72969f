# Recovery Operations Module
# System rollback and recovery functionality

# Import required modules
Import-Module "$PSScriptRoot\..\Core\Logging\Logging.psm1" -Force
Import-Module "$PSScriptRoot\Backup\BackupManagement.psm1" -Force

# Rollback Functions
function Invoke-SystemRollback {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$BackupDir,
        [hashtable]$Config
    )
    
    Write-ModuleLog "Starting system rollback from: $BackupDir" "Info"
    
    try {
        if (-not (Test-Path $BackupDir)) {
            throw "Backup directory not found: $BackupDir"
        }
        
        # Restore registry backups
        $registryBackup = Join-Path $BackupDir "registry-backup.reg"
        if (Test-Path $registryBackup) {
            Write-ModuleLog "Restoring registry from backup..." "Info"
            $result = Start-Process -FilePath "reg" -ArgumentList @("import", "`"$registryBackup`"") -Wait -PassThru
            if ($result.ExitCode -eq 0) {
                Write-ModuleLog "Registry restored successfully" "Info"
            } else {
                Write-ModuleLog "Registry restore failed with exit code: $($result.ExitCode)" "Warning"
            }
        }
        
        # Restore file backups
        $fileBackupDir = Join-Path $BackupDir "files"
        if (Test-Path $fileBackupDir) {
            Write-ModuleLog "Restoring files from backup..." "Info"
            $backupFiles = Get-ChildItem $fileBackupDir -Recurse -File
            foreach ($backupFile in $backupFiles) {
                try {
                    $relativePath = $backupFile.FullName.Substring($fileBackupDir.Length + 1)
                    $originalPath = Join-Path $env:SYSTEMROOT $relativePath
                    
                    # Create directory if needed
                    $parentDir = Split-Path $originalPath -Parent
                    if (-not (Test-Path $parentDir)) {
                        New-Item -Path $parentDir -ItemType Directory -Force | Out-Null
                    }
                    
                    Copy-Item $backupFile.FullName $originalPath -Force
                    Write-ModuleLog "Restored file: $originalPath" "Debug"
                }
                catch {
                    Write-ModuleLog "Failed to restore file $($backupFile.FullName): $($_.Exception.Message)" "Warning"
                }
            }
        }
        
        # Restore services
        $servicesBackup = Join-Path $BackupDir "services-backup.json"
        if (Test-Path $servicesBackup) {
            Write-ModuleLog "Restoring services from backup..." "Info"
            $services = Get-Content $servicesBackup | ConvertFrom-Json
            foreach ($service in $services) {
                try {
                    Set-Service -Name $service.Name -StartupType $service.StartType -ErrorAction SilentlyContinue
                    if ($service.Status -eq "Running") {
                        Start-Service -Name $service.Name -ErrorAction SilentlyContinue
                    }
                    Write-ModuleLog "Restored service: $($service.Name)" "Debug"
                }
                catch {
                    Write-ModuleLog "Failed to restore service $($service.Name): $($_.Exception.Message)" "Warning"
                }
            }
        }
        
        Write-ModuleLog "System rollback completed successfully" "Info"
        return @{ Success = $true; Message = "System rollback completed" }
    }
    catch {
        Write-ModuleLog "System rollback failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

# Recovery Validation Functions
function Test-RecoveryIntegrity {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$BackupDir
    )
    
    Write-ModuleLog "Validating recovery integrity..." "Info"
    
    try {
        $validationResults = @()
        
        # Check backup directory structure
        $requiredFiles = @("registry-backup.reg", "services-backup.json", "backup-manifest.json")
        foreach ($file in $requiredFiles) {
            $filePath = Join-Path $BackupDir $file
            if (Test-Path $filePath) {
                $validationResults += @{ Component = $file; Status = "Valid" }
            } else {
                $validationResults += @{ Component = $file; Status = "Missing" }
                Write-ModuleLog "Missing backup component: $file" "Warning"
            }
        }
        
        # Validate backup manifest
        $manifestPath = Join-Path $BackupDir "backup-manifest.json"
        if (Test-Path $manifestPath) {
            $manifest = Get-Content $manifestPath | ConvertFrom-Json
            Write-ModuleLog "Backup created: $($manifest.CreatedDate)" "Info"
            Write-ModuleLog "Backup version: $($manifest.Version)" "Info"
        }
        
        $validCount = ($validationResults | Where-Object { $_.Status -eq "Valid" }).Count
        $totalCount = $validationResults.Count
        
        Write-ModuleLog "Recovery integrity check: $validCount/$totalCount components valid" "Info"
        
        return @{
            Success = $validCount -eq $totalCount
            ValidationResults = $validationResults
            Message = "Recovery integrity: $validCount/$totalCount valid"
        }
    }
    catch {
        Write-ModuleLog "Recovery integrity check failed: $($_.Exception.Message)" "Error"
        return @{ Success = $false; Message = $_.Exception.Message }
    }
}

# Main Recovery Operations Function
function Invoke-RecoveryOperations {
    [CmdletBinding()]
    param(
        [hashtable]$Config,
        [string]$Operation = "Rollback",
        [string]$BackupPath
    )
    
    Write-ModuleLog "Starting recovery operations..." "Info"
    $results = @()
    
    switch ($Operation) {
        "Rollback" {
            if ($BackupPath) {
                $results += Invoke-SystemRollback -BackupDir $BackupPath -Config $Config
            } else {
                $results += @{ Success = $false; Message = "Backup path required for rollback" }
            }
        }
        "Validate" {
            if ($BackupPath) {
                $results += Test-RecoveryIntegrity -BackupDir $BackupPath
            } else {
                $results += @{ Success = $false; Message = "Backup path required for validation" }
            }
        }
        default {
            $results += @{ Success = $false; Message = "Unknown recovery operation: $Operation" }
        }
    }
    
    $successCount = ($results | Where-Object { $_.Success }).Count
    $totalCount = $results.Count
    
    Write-ModuleLog "Recovery operations completed: $successCount/$totalCount successful" "Info"
    
    return @{
        Success = $successCount -eq $totalCount
        Results = $results
        Summary = "Recovery operations: $successCount/$totalCount successful"
    }
}

# Export functions
Export-ModuleMember -Function @(
    'Invoke-RecoveryOperations',
    'Invoke-SystemRollback',
    'Test-RecoveryIntegrity'
)

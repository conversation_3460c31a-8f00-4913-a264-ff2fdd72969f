# Validation Module - Simplified Version
function Invoke-PrerequisiteValidation {
    param([hashtable]$Config)
    
    # Check if running as administrator
    $currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
    if (-not $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
        throw "Scrip<PERSON> must be run as Administrator"
    }
    
    # Check PowerShell version
    if ($PSVersionTable.PSVersion.Major -lt 5) {
        throw "PowerShell 5.1 or higher required"
    }
    
    return @{ Success = $true; Message = "Prerequisites validated" }
}

Export-ModuleMember -Function @('Invoke-PrerequisiteValidation')
